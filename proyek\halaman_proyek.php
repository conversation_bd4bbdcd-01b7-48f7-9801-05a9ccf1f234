<?php
require '../koneksi.php';

// Get statistics for dashboard
$total_tasks = mysqli_fetch_array(mysqli_query($koneksi, "SELECT COUNT(*) as count FROM tugas_proyek"))['count'];
$pending_tasks = mysqli_fetch_array(mysqli_query($koneksi, "SELECT COUNT(*) as count FROM tugas_proyek WHERE status = 'pending'"))['count'];
$in_progress_tasks = mysqli_fetch_array(mysqli_query($koneksi, "SELECT COUNT(*) as count FROM tugas_proyek WHERE status = 'proses'"))['count'];
$completed_tasks = mysqli_fetch_array(mysqli_query($koneksi, "SELECT COUNT(*) as count FROM tugas_proyek WHERE status = 'selesai'"))['count'];
$verification_tasks = mysqli_fetch_array(mysqli_query($koneksi, "SELECT COUNT(*) as count FROM tugas_proyek WHERE status = 'verifikasi'"))['count'];

// Get verification statistics
$total_verifications = mysqli_fetch_array(mysqli_query($koneksi, "SELECT COUNT(*) as count FROM verifikasi"))['count'];
$approved_count = mysqli_fetch_array(mysqli_query($koneksi, "SELECT COUNT(*) as count FROM verifikasi WHERE status_verifikasi = 'approved'"))['count'];
$pending_verifications = mysqli_fetch_array(mysqli_query($koneksi, "SELECT COUNT(*) as count FROM verifikasi WHERE status_verifikasi = 'pending'"))['count'];
?>

<!-- Welcome Header -->
<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">
        <i class="fas fa-tachometer-alt mr-2"></i>Dashboard Tim Proyek Antosa Arsitek
    </h1>
    <div class="text-right">
        <small class="text-muted">Selamat datang, <strong><?php echo $_SESSION['nama']; ?></strong></small>
    </div>
</div>

<!-- Statistics Cards Row -->
<div class="row">
    <!-- Total Tasks Card -->
    <div class="col-xl-3 col-lg-6 col-md-6 col-sm-6 col-12 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Tugas</div>
                        <div class="h4 mb-0 font-weight-bold text-gray-800"><?php echo $total_tasks; ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-tasks fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Pending Tasks Card -->
    <div class="col-xl-3 col-lg-6 col-md-6 col-sm-6 col-12 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Pending</div>
                        <div class="h4 mb-0 font-weight-bold text-gray-800"><?php echo $pending_tasks; ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-clock fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- In Progress Tasks Card -->
    <div class="col-xl-3 col-lg-6 col-md-6 col-sm-6 col-12 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Dalam Proses</div>
                        <div class="h4 mb-0 font-weight-bold text-gray-800"><?php echo $in_progress_tasks; ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-spinner fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Completed Tasks Card -->
    <div class="col-xl-3 col-lg-6 col-md-6 col-sm-6 col-12 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Selesai</div>
                        <div class="h4 mb-0 font-weight-bold text-gray-800"><?php echo $completed_tasks; ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions Row -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-bolt mr-2"></i>Aksi Cepat
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-xl-3 col-lg-6 col-md-6 col-sm-6 col-12 mb-3">
                        <button class="btn btn-primary btn-block btn-responsive" data-toggle="modal" data-target="#inputTugasModal">
                            <i class="fas fa-plus mr-2"></i>
                            <span class="d-none d-sm-inline">Input Tugas Baru</span>
                            <span class="d-inline d-sm-none">Input Tugas</span>
                        </button>
                    </div>
                    <div class="col-xl-3 col-lg-6 col-md-6 col-sm-6 col-12 mb-3">
                        <button class="btn btn-info btn-block btn-responsive" data-toggle="modal" data-target="#uploadFileModal">
                            <i class="fas fa-upload mr-2"></i>
                            <span class="d-none d-sm-inline">Upload File Desain</span>
                            <span class="d-inline d-sm-none">Upload File</span>
                        </button>
                    </div>
                    <div class="col-xl-3 col-lg-6 col-md-6 col-sm-6 col-12 mb-3">
                        <a href="tugas_harian.php" class="btn btn-success btn-block btn-responsive">
                            <i class="fas fa-list mr-2"></i>
                            <span class="d-none d-sm-inline">Lihat Semua Tugas</span>
                            <span class="d-inline d-sm-none">Semua Tugas</span>
                        </a>
                    </div>
                    <div class="col-xl-3 col-lg-6 col-md-6 col-sm-6 col-12 mb-3">
                        <a href="verifikasi.php" class="btn btn-warning btn-block btn-responsive">
                            <i class="fas fa-check-double mr-2"></i>
                            <span class="d-none d-sm-inline">Verifikasi</span>
                            <span class="d-inline d-sm-none">Verifikasi</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Tasks and Verification Status Row -->
<div class="row">
    <!-- Recent Tasks -->
    <div class="col-xl-8 col-lg-12 col-md-12 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-clock mr-2"></i>Tugas Terbaru
                </h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-hover" width="100%" cellspacing="0">
                        <thead class="thead-light">
                            <tr>
                                <th class="text-nowrap">Nama Tugas</th>
                                <th class="text-nowrap d-none d-md-table-cell">Tanggal</th>
                                <th class="text-nowrap">Status</th>
                                <th class="text-nowrap d-none d-lg-table-cell">Progress</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $recent_tasks = mysqli_query($koneksi, "SELECT * FROM tugas_proyek ORDER BY tgl DESC LIMIT 5");
                            while ($task = mysqli_fetch_array($recent_tasks)) {
                                $status = $task['status'];
                                $badgeClass = 'secondary';
                                $statusText = ucfirst($status);
                                $progress = 0;

                                if ($status == 'pending') {
                                    $badgeClass = 'secondary';
                                    $statusText = 'Pending';
                                    $progress = 0;
                                } else if ($status == 'proses') {
                                    $badgeClass = 'warning';
                                    $statusText = 'Dalam Proses';
                                    $progress = 50;
                                } else if ($status == 'verifikasi') {
                                    $badgeClass = 'info';
                                    $statusText = 'Siap Verifikasi';
                                    $progress = 90;
                                } else if ($status == 'selesai') {
                                    $badgeClass = 'success';
                                    $statusText = 'Selesai';
                                    $progress = 100;
                                } else if ($status == 'batal') {
                                    $badgeClass = 'danger';
                                    $statusText = 'Batal';
                                    $progress = 0;
                                }

                                // Use progress_percentage from database if it exists, otherwise use calculated progress
                                if (isset($task['progress_percentage']) && $task['progress_percentage'] !== null) {
                                    $progress = $task['progress_percentage'];
                                }
                                ?>
                                <tr>
                                    <td>
                                        <div class="d-flex flex-column">
                                            <strong><?php echo htmlspecialchars($task['nama_kegiatan']); ?></strong>
                                            <div class="d-block d-md-none">
                                                <small class="text-muted"><?php echo date('d/m/Y', strtotime($task['tgl'])); ?></small>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="d-none d-md-table-cell"><?php echo date('d/m/Y', strtotime($task['tgl'])); ?></td>
                                    <td>
                                        <span class="badge badge-<?php echo $badgeClass; ?>">
                                            <?php echo $statusText; ?>
                                        </span>
                                        <div class="d-block d-lg-none mt-1">
                                            <div class="progress" style="height: 12px;">
                                                <div class="progress-bar bg-<?php echo $badgeClass; ?>" role="progressbar"
                                                     style="width: <?php echo $progress; ?>%"
                                                     aria-valuenow="<?php echo $progress; ?>"
                                                     aria-valuemin="0" aria-valuemax="100">
                                                    <small><?php echo $progress; ?>%</small>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="d-none d-lg-table-cell">
                                        <div class="progress" style="height: 15px;">
                                            <div class="progress-bar bg-<?php echo $badgeClass; ?>" role="progressbar"
                                                 style="width: <?php echo $progress; ?>%"
                                                 aria-valuenow="<?php echo $progress; ?>"
                                                 aria-valuemin="0" aria-valuemax="100">
                                                <?php echo $progress; ?>%
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            <?php } ?>
                        </tbody>
                    </table>
                </div>
                <div class="text-center mt-3">
                    <a href="tugas_harian.php" class="btn btn-primary btn-sm">
                        <i class="fas fa-eye mr-1"></i>Lihat Semua Tugas
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Verification Summary -->
    <div class="col-xl-4 col-lg-6 col-md-12 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-check-double mr-2"></i>Status Verifikasi
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="text-sm">Total Verifikasi</span>
                        <span class="font-weight-bold"><?php echo $total_verifications; ?></span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="text-sm text-success">Disetujui</span>
                        <span class="font-weight-bold text-success"><?php echo $approved_count; ?></span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="text-sm text-warning">Pending</span>
                        <span class="font-weight-bold text-warning"><?php echo $pending_verifications; ?></span>
                    </div>
                </div>
                <div class="text-center">
                    <a href="verifikasi.php" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-external-link-alt mr-1"></i>Detail Verifikasi
                    </a>
                </div>
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="card shadow mt-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-chart-pie mr-2"></i>Ringkasan Hari Ini
                </h6>
            </div>
            <div class="card-body">
                <?php
                $today = date('Y-m-d');
                $today_tasks = mysqli_fetch_array(mysqli_query($koneksi, "SELECT COUNT(*) as count FROM tugas_proyek WHERE DATE(tgl) = '$today'"))['count'];
                $today_completed = mysqli_fetch_array(mysqli_query($koneksi, "SELECT COUNT(*) as count FROM tugas_proyek WHERE DATE(tgl) = '$today' AND status = 'selesai'"))['count'];
                ?>
                <div class="text-center">
                    <div class="mb-2">
                        <span class="h4 font-weight-bold text-primary"><?php echo $today_tasks; ?></span>
                        <div class="text-sm text-muted">Tugas Hari Ini</div>
                    </div>
                    <div class="mb-2">
                        <span class="h4 font-weight-bold text-success"><?php echo $today_completed; ?></span>
                        <div class="text-sm text-muted">Selesai Hari Ini</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal Input Tugas -->
<div class="modal fade" id="inputTugasModal" tabindex="-1" role="dialog" aria-labelledby="inputTugasModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered modal-dialog-scrollable" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="inputTugasModalLabel">
                    <i class="fas fa-plus mr-2"></i>Input Tugas Baru
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form action="simpan_input.php" method="post" enctype="multipart/form-data">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="nama_kegiatan">Nama Kegiatan</label>
                        <input type="text" class="form-control" id="nama_kegiatan" name="nama_kegiatan"
                            placeholder="Contoh: Pengecoran Lantai 2" required>
                    </div>

                    <div class="form-group">
                        <label for="deskripsi">Deskripsi Pekerjaan</label>
                        <textarea class="form-control" id="deskripsi" name="deskripsi" rows="3"
                            placeholder="Deskripsikan progres pekerjaan di lapangan" required></textarea>
                    </div>

                    <div class="form-group">
                        <label for="tgl">Tanggal Pengerjaan</label>
                        <input type="date" class="form-control" id="tgl" name="tgl" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save mr-1"></i>Simpan Tugas
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Upload File -->
<div class="modal fade" id="uploadFileModal" tabindex="-1" role="dialog" aria-labelledby="uploadFileModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered modal-dialog-scrollable" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="uploadFileModalLabel">
                    <i class="fas fa-upload mr-2"></i>Upload File Desain
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form action="simpan_file.php" method="post" enctype="multipart/form-data">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="deskripsi_file">Deskripsi File</label>
                        <input type="text" name="deskripsi" class="form-control"
                            placeholder="Masukkan deskripsi..." maxlength="200" required>
                        <small class="form-text text-muted">Maksimal 200 karakter</small>
                    </div>

                    <div class="form-group">
                        <label for="file_upload">Pilih File Desain</label>
                        <input type="file" name="gambar" class="form-control-file" required>
                        <small class="form-text text-muted">
                            File yang diperbolehkan: .jpg, .png, .pdf, .obj, .stl, .dwg (Maksimal 50MB)
                        </small>
                    </div>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle mr-2"></i>
                        <strong>Tips:</strong> Pastikan file desain sudah dalam format yang benar dan ukuran tidak melebihi 50MB.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-upload mr-1"></i>Upload File
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>