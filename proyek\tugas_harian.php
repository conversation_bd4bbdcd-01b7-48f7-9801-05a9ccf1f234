<!DOCTYPE html>
<html lang="en">

<head>

    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="">
    <meta name="author" content="">

    <title>SB Admin 2 - Dashboard</title>

    <!-- Custom fonts for this template-->
    <link href="../tmp/vendor/fontawesome-free/css/all.min.css" rel="stylesheet" type="text/css">
    <link
        href="https://fonts.googleapis.com/css?family=Nunito:200,200i,300,300i,400,400i,600,600i,700,700i,800,800i,900,900i"
        rel="stylesheet">

    <!-- Custom styles for this template-->
    <link href="../tmp/css/sb-admin-2.min.css" rel="stylesheet">

    <!-- Custom responsive styles for dashboard -->
    <link href="css/dashboard-responsive.css" rel="stylesheet">

</head>


<body id="page-top">

    <!-- Page Wrapper -->
    <div id="wrapper">

        <!-- Sidebar -->
        <ul class="navbar-nav bg-gradient-primary sidebar sidebar-dark accordion" id="accordionSidebar">

            <!-- Sidebar - Brand -->
            <a class="sidebar-brand d-flex align-items-center justify-content-center" href="index.html">
                <div class="sidebar-brand-icon rotate-n-15">
                    <i class="fas fa-keyboard"></i>
                </div>
                <div class="sidebar-brand-text mx-3">APPEMSZ</div>
            </a>

            <!-- Divider -->
            <hr class="sidebar-divider my-0">

            <!-- Nav Item - Dashboard -->
            <li class="nav-item">
                <a class="nav-link" href="proyek.php">
                    <i class="fas fa-fw fa-tachometer-alt"></i>
                    <span>Dashboard</span></a>
            </li>


            <!-- Divider -->
            <hr class="sidebar-divider">

            <!-- Heading -->
            <div class="sidebar-heading">
                Interface
            </div>



            <li class="nav-item active">
                <a class="nav-link" href="tugas_harian.php">
                    <i class="fas fa-fw fa-tasks"></i>
                    <span>Tugas harian</span></a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="uploud_file.php">
                    <i class="fas fa-fw fa-upload"></i>
                    <span>Upload file desain</span></a>
            </li>

            <li class="nav-item">
                <a class="nav-link" href="client_files.php">
                    <i class="fas fa-fw fa-folder-open"></i>
                    <span>File dari Client</span></a>
            </li>

            <li class="nav-item">
                <a class="nav-link" href="verifikasi.php">
                    <i class="fas fa-fw fa-check-circle"></i>
                    <span>Verifikasi</span></a>
            </li>

            <li class="nav-item">
                <a class="nav-link" href="input_tugas.php">
                    <i class="fas fa-fw fa-plus"></i>
                    <span>input tugas harian</span></a>
            </li>

            <li class="nav-item">
                <a class="nav-link" href="kelola_revisi.php">
                    <i class="fas fa-fw fa-edit"></i>
                    <span>Kelola Revisi</span></a>
            </li>
            <!-- Divider -->
            <hr class="sidebar-divider d-none d-md-block">

            <li class="nav-item">
                <a class="nav-link" href="../logout.php">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Keluar</span></a>
            </li>

            <!-- Sidebar Toggler (Sidebar) -->
            <div class="text-center d-none d-md-inline">
                <button class="rounded-circle border-0" id="sidebarToggle"></button>
            </div>

        </ul>
        <!-- End of Sidebar -->

        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">

            <!-- Main Content -->
            <div id="content">

                <!-- Topbar -->
                <nav class="navbar navbar-expand navbar-light bg-white topbar mb-4 static-top shadow">

                    <!-- Sidebar Toggle (Topbar) -->
                    <button id="sidebarToggleTop" class="btn btn-link d-md-none rounded-circle mr-3">
                        <i class="fa fa-bars"></i>
                    </button>

                    <h1></h1>

                </nav>
                <div class="container mt-5">
                    <h2 class="mb-4">Lihat List Tugas Harian Proyek Arsitek</h2>
                    <table class="table table-bordered">
                        <thead class="thead-dark">
                            <tr>
                                <th>No</th>
                                <th>Nama Tugas</th>
                                <th>Deskripsi</th>
                                <th>Tanggal</th>
                                <th>Progress</th>
                                <th>Status</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody>

                            <?php
                            require '../koneksi.php';
                            $sql = mysqli_query($koneksi, "select * from tugas_proyek");
                            while ($data = mysqli_fetch_array($sql)) {

                                ?>
                                <tr>
                                    <td><?php echo $data['id']; ?></td>
                                    <td><strong><?php echo htmlspecialchars($data['nama_kegiatan']); ?></strong></td>
                                    <td><?php echo htmlspecialchars($data['deskripsi']); ?></td>
                                    <td><?php echo date('d/m/Y', strtotime($data['tgl'])); ?></td>
                                    <td>
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar bg-<?php echo $badgeClass; ?>" role="progressbar"
                                                 style="width: <?php echo $data['progress_percentage']; ?>%"
                                                 aria-valuenow="<?php echo $data['progress_percentage']; ?>"
                                                 aria-valuemin="0" aria-valuemax="100">
                                                <?php echo $data['progress_percentage']; ?>%
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <?php
                                        $status = $data['status'];
                                        $badgeClass = 'secondary';
                                        $statusText = ucfirst($status);

                                        if ($status == 'pending') {
                                            $badgeClass = 'secondary';
                                            $statusText = 'Pending';
                                        } else if ($status == 'proses') {
                                            $badgeClass = 'warning';
                                            $statusText = 'Dalam Proses';
                                        } else if ($status == 'verifikasi') {
                                            $badgeClass = 'info';
                                            $statusText = 'Siap Verifikasi';
                                        } else if ($status == 'selesai') {
                                            $badgeClass = 'success';
                                            $statusText = 'Selesai';
                                        } else if ($status == 'batal') {
                                            $badgeClass = 'danger';
                                            $statusText = 'Batal';
                                        }
                                        ?>

                                        <span class="badge badge-<?php echo $badgeClass; ?> p-2">
                                            <?php echo $statusText; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <!-- Action Button with Modal Trigger -->
                                        <button type="button" class="btn btn-primary btn-sm"
                                            data-toggle="modal" data-target="#updateStatusModal<?php echo $data['id']; ?>">
                                            <i class="fas fa-edit mr-1"></i>Update
                                        </button>

                                        <!-- Modal Update Status -->
                                        <div class="modal fade" id="updateStatusModal<?php echo $data['id']; ?>"
                                            tabindex="-1" role="dialog"
                                            aria-labelledby="statusModalLabel<?php echo $data['id']; ?>" aria-hidden="true">
                                            <div class="modal-dialog" role="document">
                                                <form action="update_tugas.php" method="POST">
                                                    <input type="hidden" name="id" value="<?php echo $data['id']; ?>">
                                                    <div class="modal-content">
                                                        <div class="modal-header">
                                                            <h5 class="modal-title">Ubah Status</h5>
                                                            <button type="button" class="close" data-dismiss="modal"
                                                                aria-label="Close"><span>&times;</span></button>
                                                        </div>
                                                        <div class="modal-body">
                                                            <div class="form-group">
                                                                <label for="status">Status Tugas:</label>
                                                                <select name="status" class="form-control" required>
                                                                    <option value="pending" <?php if ($status == 'pending') echo 'selected'; ?>>Pending</option>
                                                                    <option value="proses" <?php if ($status == 'proses') echo 'selected'; ?>>Dalam Proses</option>
                                                                    <option value="verifikasi" <?php if ($status == 'verifikasi') echo 'selected'; ?>>Siap Verifikasi</option>
                                                                    <option value="selesai" <?php if ($status == 'selesai') echo 'selected'; ?>>Selesai</option>
                                                                    <option value="batal" <?php if ($status == 'batal') echo 'selected'; ?>>Batal</option>
                                                                </select>
                                                                <small class="form-text text-muted">
                                                                    Pilih "Siap Verifikasi" jika tugas sudah selesai dan perlu diverifikasi oleh supervisor.
                                                                </small>
                                                            </div>
                                                        </div>
                                                        <div class="modal-footer">
                                                            <button type="submit" class="btn btn-primary">Simpan</button>
                                                            <button type="button" class="btn btn-secondary"
                                                                data-dismiss="modal">Batal</button>
                                                        </div>
                                                    </div>
                                                </form>
                                            </div>
                                        </div>
                                    </td>




                                </tr>
                            </tbody> <?php } ?>
                    </table>
                </div>


                <!-- End of Main Content -->

                <!-- Footer -->
                <footer class="sticky-footer bg-white">
                    <div class="container my-auto">
                        <div class="copyright text-center my-auto">
                            <span>Copyright &copy;FOKUS UKK!!</span>
                        </div>
                    </div>
                </footer>
                <!-- End of Footer -->

            </div>
            <!-- End of Content Wrapper -->


            <!-- Scroll to Top Button-->
            <a class="scroll-to-top rounded" href="#page-top">
                <i class="fas fa-angle-up"></i>
            </a>

            <!-- Logout Modal-->
            <div class="modal fade" id="logoutModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
                aria-hidden="true">
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="exampleModalLabel">Ready to Leave?</h5>
                            <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">×</span>
                            </button>
                        </div>
                        <div class="modal-body">Select "Logout" below if you are ready to end your current session.
                        </div>
                        <div class="modal-footer">
                            <button class="btn btn-secondary" type="button" data-dismiss="modal">Cancel</button>
                            <a class="btn btn-primary" href="login.php">Logout</a>
                        </div>
                    </div>
                </div>
            </div>




        </div> <!-- End of Content Wrapper -->
    </div> <!-- End of Page Wrapper -->

    <!-- Bootstrap core JavaScript -->
    <script src="../tmp/vendor/jquery/jquery.min.js"></script>
    <script src="../tmp/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>

    <!-- Core plugin JavaScript -->
    <script src="../tmp/vendor/jquery-easing/jquery.easing.min.js"></script>

    <!-- Custom scripts for all pages -->
    <script src="../tmp/js/sb-admin-2.min.js"></script>

    <!-- Optional: Chart plugin -->
    <script src="../tmp/vendor/chart.js/Chart.min.js"></script>
    <script src="../tmp/js/demo/chart-area-demo.js"></script>
    <script src="../tmp/js/demo/chart-pie-demo.js"></script>

</body>

</html>