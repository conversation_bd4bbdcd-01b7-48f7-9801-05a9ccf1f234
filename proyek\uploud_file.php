<!DOCTYPE html>
<html lang="en">

<head>

    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="">
    <meta name="author" content="">

    <title>SB Admin 2 - Dashboard</title>

    <!-- Custom fonts for this template-->
    <link href="../tmp/vendor/fontawesome-free/css/all.min.css" rel="stylesheet" type="text/css">
    <link
        href="https://fonts.googleapis.com/css?family=Nunito:200,200i,300,300i,400,400i,600,600i,700,700i,800,800i,900,900i"
        rel="stylesheet">

    <!-- Custom styles for this template-->
    <link href="../tmp/css/sb-admin-2.min.css" rel="stylesheet">

    <!-- Custom responsive styles for dashboard -->
    <link href="css/dashboard-responsive.css" rel="stylesheet">

</head>


<body id="page-top">

    <!-- Page Wrapper -->
    <div id="wrapper">

        <!-- Sidebar -->
        <ul class="navbar-nav bg-gradient-primary sidebar sidebar-dark accordion" id="accordionSidebar">

            <!-- Sidebar - Brand -->
            <a class="sidebar-brand d-flex align-items-center justify-content-center" href="index.html">
                <div class="sidebar-brand-icon rotate-n-15">
                    <i class="fas fa-keyboard"></i>
                </div>
                <div class="sidebar-brand-text mx-3">APPEMSZ</div>
            </a>

            <!-- Divider -->
            <hr class="sidebar-divider my-0">

            <!-- Nav Item - Dashboard -->
            <li class="nav-item">
                <a class="nav-link" href="proyek.php">
                    <i class="fas fa-fw fa-tachometer-alt"></i>
                    <span>Dashboard</span></a>
            </li>


            <!-- Divider -->
            <hr class="sidebar-divider">

            <!-- Heading -->
            <div class="sidebar-heading">
                Interface
            </div>



            <li class="nav-item">
                <a class="nav-link" href="tugas_harian.php">
                    <i class="fas fa-fw fa-tasks"></i>
                    <span>Tugas harian</span></a>
            </li>

            <li class="nav-item active">
                <a class="nav-link" href="uploud_file.php">
                    <i class="fas fa-fw fa-upload"></i>
                    <span>Upload file desain</span></a>
            </li>

            <li class="nav-item">
                <a class="nav-link" href="client_files.php">
                    <i class="fas fa-fw fa-folder-open"></i>
                    <span>File dari Client</span></a>
            </li>

            <li class="nav-item">
                <a class="nav-link" href="verifikasi.php">
                    <i class="fas fa-fw fa-check-circle"></i>
                    <span>Verifikasi</span></a>
            </li>

            <li class="nav-item">
                <a class="nav-link" href="input_tugas.php">
                    <i class="fas fa-fw fa-plus"></i>
                    <span>input tugas harian</span></a>
            </li>

            <li class="nav-item">
                <a class="nav-link" href="kelola_revisi.php">
                    <i class="fas fa-fw fa-edit"></i>
                    <span>Kelola Revisi</span></a>
            </li>
            <!-- Divider -->
            <hr class="sidebar-divider d-none d-md-block">

            <li class="nav-item">
                <a class="nav-link" href="../logout.php">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Keluar</span></a>
            </li>

            <!-- Sidebar Toggler (Sidebar) -->
            <div class="text-center d-none d-md-inline">
                <button class="rounded-circle border-0" id="sidebarToggle"></button>
            </div>

        </ul>
        <!-- End of Sidebar -->

        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">

            <!-- Main Content -->
            <div id="content">
                <div class="container-fluid mt-4">
                    <h1 class="h3 mb-4 text-gray-800">Upload File Desain</h1>
                    <div class="card shadow mb-4">
                        <div class="card-body">
                            <form action="simpan_file.php" method="post" enctype="multipart/form-data">
                                <div class="form-group">
                                    <label for="deskripsi">Deskripsi File</label>
                                    <input type="text" name="deskripsi" class="form-control"
                                        placeholder="Masukkan deskripsi..." maxlength="200" required>
                                    <small class="form-text text-muted">Maksimal 200 karakter</small>
                                </div>

                                <div class="form-group">
                                    <label for="file">Pilih File Desain</label>
                                    <input type="file" name="gambar" class="form-control-file" required>
                                    <small class="form-text text-muted">File yang diperbolehkan: .jpg, .png, .pdf, .obj,
                                        .stl, .dwg (Maksimal 50MB)</small>
                                </div>

                                <button type="submit" class="btn btn-primary">Upload</button>
                            </form>

                        </div>
                    </div>
                </div>
            </div>



            <!-- Footer -->
            <footer class="sticky-footer bg-white">
                <div class="container my-auto">
                    <div class="copyright text-center my-auto">
                        <span>Copyright &copy;FOKUS UKK!!</span>
                    </div>
                </div>
            </footer>
            <!-- End of Footer -->

        </div>
        <!-- End of Content Wrapper -->


        <!-- Scroll to Top Button-->
        <a class="scroll-to-top rounded" href="#page-top">
            <i class="fas fa-angle-up"></i>
        </a>

        <!-- Logout Modal-->
        <div class="modal fade" id="logoutModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
            aria-hidden="true">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="exampleModalLabel">Ready to Leave?</h5>
                        <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">×</span>
                        </button>
                    </div>
                    <div class="modal-body">Select "Logout" below if you are ready to end your current session.</div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" type="button" data-dismiss="modal">Cancel</button>
                        <a class="btn btn-primary" href="login.php">Logout</a>
                    </div>
                </div>
            </div>
        </div>




    </div> <!-- End of Content Wrapper -->
    </div> <!-- End of Page Wrapper -->

    <!-- Bootstrap core JavaScript -->
    <script src="../tmp/vendor/jquery/jquery.min.js"></script>
    <script src="../tmp/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>

    <!-- Core plugin JavaScript -->
    <script src="../tmp/vendor/jquery-easing/jquery.easing.min.js"></script>

    <!-- Custom scripts for all pages -->
    <script src="../tmp/js/sb-admin-2.min.js"></script>

    <!-- Optional: Chart plugin -->
    <script src="../tmp/vendor/chart.js/Chart.min.js"></script>
    <script src="../tmp/js/demo/chart-area-demo.js"></script>
    <script src="../tmp/js/demo/chart-pie-demo.js"></script>

</body>

</html>