/* Custom Responsive CSS for Dashboard */

/* Mobile-first responsive improvements */
@media (max-width: 575.98px) {
    /* Header improvements */
    .h3 {
        font-size: 1.5rem !important;
    }
    
    /* Card improvements */
    .card-body {
        padding: 1rem 0.75rem;
    }
    
    /* Button improvements */
    .btn-responsive {
        padding: 0.5rem 0.75rem;
        font-size: 0.875rem;
    }
    
    /* Table improvements */
    .table td {
        padding: 0.5rem 0.25rem;
        font-size: 0.875rem;
    }
    
    .table th {
        padding: 0.5rem 0.25rem;
        font-size: 0.875rem;
        font-weight: 600;
    }
    
    /* Progress bar improvements */
    .progress {
        height: 10px !important;
        font-size: 0.75rem;
    }
    
    /* Badge improvements */
    .badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }
    
    /* Modal improvements */
    .modal-dialog {
        margin: 0.5rem;
        max-width: calc(100% - 1rem);
    }
    
    .modal-body {
        padding: 1rem;
    }
    
    /* Form improvements */
    .form-control {
        font-size: 1rem; /* Prevent zoom on iOS */
    }
    
    /* Container improvements */
    .container-fluid {
        padding-left: 0.75rem;
        padding-right: 0.75rem;
    }
}

/* Small devices (landscape phones, 576px and up) */
@media (min-width: 576px) and (max-width: 767.98px) {
    .btn-responsive {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }
    
    .card-body {
        padding: 1.25rem 1rem;
    }
}

/* Medium devices (tablets, 768px and up) */
@media (min-width: 768px) and (max-width: 991.98px) {
    /* Sidebar adjustments for tablets */
    .sidebar {
        width: 12rem !important;
    }
    
    .sidebar .nav-item .nav-link span {
        font-size: 0.85rem;
    }
    
    /* Card adjustments */
    .card-body {
        padding: 1.25rem;
    }
}

/* Large devices (desktops, 992px and up) */
@media (min-width: 992px) {
    /* Ensure proper spacing on larger screens */
    .card-body {
        padding: 1.5rem;
    }
}

/* Custom utility classes for better responsive control */
.text-responsive {
    font-size: 0.875rem;
}

@media (min-width: 768px) {
    .text-responsive {
        font-size: 1rem;
    }
}

/* Improved card hover effects */
.card:hover {
    transform: translateY(-2px);
    transition: transform 0.2s ease-in-out;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

/* Better focus states for accessibility */
.btn:focus,
.form-control:focus {
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

/* Improved table responsiveness */
@media (max-width: 767.98px) {
    .table-responsive {
        border: none;
    }
    
    .table-responsive .table {
        margin-bottom: 0;
    }
    
    .table-responsive .table td,
    .table-responsive .table th {
        white-space: nowrap;
    }
}

/* Loading states and animations */
.card {
    transition: all 0.3s ease;
}

.progress-bar {
    transition: width 0.6s ease;
}

/* Print styles */
@media print {
    .sidebar,
    .topbar,
    .btn,
    .modal {
        display: none !important;
    }
    
    .card {
        border: 1px solid #dee2e6 !important;
        box-shadow: none !important;
    }
    
    .card-body {
        padding: 1rem !important;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .card {
        border: 2px solid #000;
    }
    
    .btn {
        border: 2px solid;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .card,
    .progress-bar,
    * {
        transition: none !important;
        animation: none !important;
    }
}

/* Dark mode support (if needed in future) */
@media (prefers-color-scheme: dark) {
    /* Placeholder for dark mode styles */
}

/* Landscape orientation adjustments for mobile */
@media (max-width: 767.98px) and (orientation: landscape) {
    .modal-dialog {
        max-height: 90vh;
        overflow-y: auto;
    }
    
    .card-body {
        padding: 0.75rem;
    }
}

/* Very small screens (less than 360px) */
@media (max-width: 359.98px) {
    .h3 {
        font-size: 1.25rem !important;
    }
    
    .btn-responsive {
        padding: 0.375rem 0.5rem;
        font-size: 0.8rem;
    }
    
    .card-body {
        padding: 0.75rem 0.5rem;
    }
    
    .container-fluid {
        padding-left: 0.5rem;
        padding-right: 0.5rem;
    }
}

/* Accessibility improvements */
.sr-only-focusable:active,
.sr-only-focusable:focus {
    position: static;
    width: auto;
    height: auto;
    overflow: visible;
    clip: auto;
    white-space: normal;
}

/* Better touch targets for mobile */
@media (max-width: 767.98px) {
    .btn,
    .nav-link,
    .dropdown-item {
        min-height: 44px;
        display: flex;
        align-items: center;
    }
}
