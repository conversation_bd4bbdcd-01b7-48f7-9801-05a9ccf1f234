<?php
session_start();

echo "<h2>Debug Session Information</h2>";

echo "<h3>Session Variables:</h3>";
echo "<pre>";
print_r($_SESSION);
echo "</pre>";

echo "<h3>Session Status:</h3>";
echo "<p>Session ID: " . session_id() . "</p>";
echo "<p>Session Status: " . session_status() . "</p>";

if (isset($_SESSION['nama'])) {
    echo "<p style='color: green;'>✓ User is logged in as: " . $_SESSION['nama'] . "</p>";
} else {
    echo "<p style='color: red;'>✗ User is not logged in</p>";
}

if (isset($_SESSION['level'])) {
    echo "<p style='color: green;'>✓ User level: " . $_SESSION['level'] . "</p>";
} else {
    echo "<p style='color: red;'>✗ User level not set</p>";
}

if (isset($_SESSION['id_petugas'])) {
    echo "<p style='color: green;'>✓ User ID: " . $_SESSION['id_petugas'] . "</p>";
} else {
    echo "<p style='color: red;'>✗ User ID not set</p>";
}

echo "<h3>Database Connection Test:</h3>";
require '../koneksi.php';

if ($koneksi) {
    echo "<p style='color: green;'>✓ Database connection successful</p>";
    
    // Test if file_gambar table exists and has required columns
    $check_table = mysqli_query($koneksi, "DESCRIBE file_gambar");
    if ($check_table) {
        echo "<p style='color: green;'>✓ file_gambar table exists</p>";
        echo "<h4>Table Structure:</h4>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        while ($row = mysqli_fetch_assoc($check_table)) {
            echo "<tr>";
            echo "<td>" . $row['Field'] . "</td>";
            echo "<td>" . $row['Type'] . "</td>";
            echo "<td>" . $row['Null'] . "</td>";
            echo "<td>" . $row['Key'] . "</td>";
            echo "<td>" . $row['Default'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: red;'>✗ file_gambar table not found</p>";
    }
} else {
    echo "<p style='color: red;'>✗ Database connection failed: " . mysqli_connect_error() . "</p>";
}

echo "<h3>File Upload Test:</h3>";
echo "<p>Max file size: " . ini_get('upload_max_filesize') . "</p>";
echo "<p>Max post size: " . ini_get('post_max_size') . "</p>";
echo "<p>File uploads enabled: " . (ini_get('file_uploads') ? 'Yes' : 'No') . "</p>";

echo "<h3>Directory Permissions:</h3>";
$upload_dir = '../file_proyek/';
if (is_dir($upload_dir)) {
    echo "<p style='color: green;'>✓ Upload directory exists: " . $upload_dir . "</p>";
    if (is_writable($upload_dir)) {
        echo "<p style='color: green;'>✓ Upload directory is writable</p>";
    } else {
        echo "<p style='color: red;'>✗ Upload directory is not writable</p>";
    }
} else {
    echo "<p style='color: red;'>✗ Upload directory does not exist: " . $upload_dir . "</p>";
}

echo "<h3>Navigation Links:</h3>";
echo "<p><a href='upload_brief.php'>Go to Upload Page</a></p>";
echo "<p><a href='client.php'>Go to Dashboard</a></p>";
echo "<p><a href='../update_database.php'>Update Database Schema</a></p>";
?>
